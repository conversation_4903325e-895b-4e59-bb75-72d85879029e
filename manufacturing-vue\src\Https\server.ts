import { http } from "./axiosHttps";

const readBasicUrl= import.meta.env.VITE_ReadAPP_API_URL
const writeBasicUrl=import.meta.env.VITE_WriteAPP_API_URL;

// 仓库管理API配置 - 可以快速切换不同端口进行测试
const warehouseApiUrl = 'http://localhost:5107/';  // 首选：5107端口
// const warehouseApiUrl = 'http://localhost:5062/';  // 备选：5062端口
// const warehouseApiUrl = 'http://localhost:5251/';  // 备选：5251端口
//登录
export const login =(params={})=>{
    return http("get",readBasicUrl + `api/Login/Login`,{},params);
}
//#region 用户
//显示
export const getUser =(params={})=>{
    return http("get",readBasicUrl + `api/User/GetUser`,{},params);
}
//添加
export const addUser =(data={})=>{
    return http("post",writeBasicUrl + `api/User/AddUser`,data,{});
}
//修改
export const updateUser =(data={})=>{
    return http("post",writeBasicUrl + `api/User/UpdateUser`,data,{});
}
//修改
export const updateUserState =(data={})=>{
    return http("post",writeBasicUrl + `api/User/UpdateUserState`,data,{});
}
//删除
export const deleteUser =(data={})=>{
    return http("post",writeBasicUrl + `api/User/DeleteUser`,data,{});
}
//#endregion

//#region 角色
//获取所有角色
export const getALLRole =(params={})=>{
    return http("get",readBasicUrl + `api/Role/GetAllRole`,{},params);
}
//获取角色列表
export const getRole =(params={})=>{
    return http("get",readBasicUrl + `api/Role/GetRole`,{},params);
}
//添加角色
export const addRole =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/AddRole`,data,{});
}
//修改角色
export const updateRole =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/UpdateRole`,data,{});
}
//修改角色
export const updateRoleState =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/UpdateRoleState`,data,{});
}
//删除角色
export const deleteRole =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/DeleteRole`,data,{});
}
//#endregion

//#region  权限
//获取所有权限
export const getPermission =(params={})=>{
    return http("get",readBasicUrl + `api/Permission/GetPermission`,{},params);
}
//获取权限级联
export const getMenu =(params={})=>{
    return http("get",readBasicUrl + `api/Permission/GetCascadeItem`,{},params);
}
//获取全部权限
export const getAll =(params={})=>{
    return http("get",readBasicUrl + `api/Permission/GetAllPermission`,{},params);
}
//添加权限
export const addPermission=(data={})=>{
    return http("post",writeBasicUrl + `api/Permission/CreatePermission`,data,{});
}
//修改权限
export const updatePermission =(data={})=>{
    return http("post",writeBasicUrl + `api/Permission/UpdatePermission`,data,{});
}
//删除权限
export const deletePermission =(data={})=>{
    return http("post",writeBasicUrl + `api/Permission/Handle`,data,{});
}
//#endregion

//#region 仓库管理
//获取仓库列表
export const getWarehouseList = (params = {}) => {
    // 根据测试结果，正确的API是GET方法的/api/Warehouse/list
    return http("get", warehouseApiUrl + `api/Warehouse/list`, {}, params);
}
//添加仓库
export const addWarehouse = (data = {}) => {
    return http("post", warehouseApiUrl + `api/Warehouse`, data, {});
}
//修改仓库
export const updateWarehouse = (data = {}) => {
    return http("put", warehouseApiUrl + `api/Warehouse`, data, {});
}
//删除仓库
export const deleteWarehouse = (id: number) => {
    return http("delete", warehouseApiUrl + `api/Warehouse/${id}`, {}, {});
}
//获取仓库详情
export const getWarehouseById = (id: number) => {
    return http("get", warehouseApiUrl + `api/Warehouse/${id}`, {}, {});
}
//获取仓库分类列表 - 尝试多个可能的端点
export const getWarehouseCategoryList = (params = {}) => {
    // 尝试最可能的API路径
    return http("get", warehouseApiUrl + `api/WarehouseCategory/list`, {}, params);
    // 备选路径：
    // return http("get", warehouseApiUrl + `api/Category/list`, {}, params);
    // return http("get", warehouseApiUrl + `api/WarehouseCategory`, {}, params);
}
//获取存储类型列表 - 尝试多个可能的端点
export const getStorageTypeList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/StorageType/list`, {}, params);
    // 备选路径：
    // return http("get", warehouseApiUrl + `api/StorageType`, {}, params);
}
//获取仓库结构列表 - 尝试多个可能的端点
export const getWarehouseStructureList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/WarehouseStructure/list`, {}, params);
    // 备选路径：
    // return http("get", warehouseApiUrl + `api/Structure/list`, {}, params);
}
//获取人员列表 - 尝试多个可能的端点
export const getPersonList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/Person/list`, {}, params);
    // 备选路径：
    // return http("get", warehouseApiUrl + `api/User/list`, {}, params);
    // return http("get", warehouseApiUrl + `api/Employee/list`, {}, params);
}
//#endregion

