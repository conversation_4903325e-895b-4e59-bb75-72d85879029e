{"Version": 1, "WorkspaceRootPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E7D81204-C61A-4366-80D3-312EF35B90CF}|Manufacturings.Domain\\Manufacturings.Domain.csproj|d:\\实训一\\项目\\ems\\manufacturing-vs\\manufacturings.domain\\entities\\warehouses\\warehousecategory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7D81204-C61A-4366-80D3-312EF35B90CF}|Manufacturings.Domain\\Manufacturings.Domain.csproj|solutionrelative:manufacturings.domain\\entities\\warehouses\\warehousecategory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}|ManufacturingsERP.API\\ManufacturingsERP.API.csproj|d:\\实训一\\项目\\ems\\manufacturing-vs\\manufacturingserp.api\\application\\handler\\warehouse\\getwarehouselisthandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}|ManufacturingsERP.API\\ManufacturingsERP.API.csproj|solutionrelative:manufacturingserp.api\\application\\handler\\warehouse\\getwarehouselisthandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}|ManufacturingsERP.API\\ManufacturingsERP.API.csproj|d:\\实训一\\项目\\ems\\manufacturing-vs\\manufacturingserp.api\\application\\command\\warehouse\\getwarehouselistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}|ManufacturingsERP.API\\ManufacturingsERP.API.csproj|solutionrelative:manufacturingserp.api\\application\\command\\warehouse\\getwarehouselistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "WarehouseCategory.cs", "DocumentMoniker": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Entities\\Warehouses\\WarehouseCategory.cs", "RelativeDocumentMoniker": "Manufacturings.Domain\\Entities\\Warehouses\\WarehouseCategory.cs", "ToolTip": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Entities\\Warehouses\\WarehouseCategory.cs", "RelativeToolTip": "Manufacturings.Domain\\Entities\\Warehouses\\WarehouseCategory.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T02:02:21.718Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "GetWarehouseListHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\ManufacturingsERP.API\\Application\\Handler\\Warehouse\\GetWarehouseListHandler.cs", "RelativeDocumentMoniker": "ManufacturingsERP.API\\Application\\Handler\\Warehouse\\GetWarehouseListHandler.cs", "ToolTip": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\ManufacturingsERP.API\\Application\\Handler\\Warehouse\\GetWarehouseListHandler.cs", "RelativeToolTip": "ManufacturingsERP.API\\Application\\Handler\\Warehouse\\GetWarehouseListHandler.cs", "ViewState": "AQIAABMAAAAAAAAAAAAgwCUAAAARAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T00:53:49.061Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GetWarehouseListQuery.cs", "DocumentMoniker": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\ManufacturingsERP.API\\Application\\command\\Warehouse\\GetWarehouseListQuery.cs", "RelativeDocumentMoniker": "ManufacturingsERP.API\\Application\\command\\Warehouse\\GetWarehouseListQuery.cs", "ToolTip": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\ManufacturingsERP.API\\Application\\command\\Warehouse\\GetWarehouseListQuery.cs", "RelativeToolTip": "ManufacturingsERP.API\\Application\\command\\Warehouse\\GetWarehouseListQuery.cs", "ViewState": "AQIAAA4AAAAAAAAAAAAkwBkAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-15T00:53:20.856Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}]}]}]}