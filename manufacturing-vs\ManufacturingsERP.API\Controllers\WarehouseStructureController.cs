using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Manufacturings.Infrastructrue;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Infrastructrue.Error;

namespace ManufacturingsERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WarehouseStructureController : ControllerBase
    {
        private readonly MyDbContext _context;

        public WarehouseStructureController(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取仓库结构列表
        /// </summary>
        /// <param name="pageSize">每页大小</param>
        /// <param name="page">页码</param>
        /// <param name="keyword">搜索关键词</param>
        /// <returns></returns>
        [HttpGet("list")]
        public async Task<IActionResult> GetWarehouseStructureList(
            int pageSize = 20,
            int page = 1,
            string keyword = "")
        {
            try
            {
                var query = _context.WarehouseStructures.AsQueryable();

                // 搜索过滤
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.StructureName.Contains(keyword) ||
                                           x.Description.Contains(keyword));
                }

                // 总数统计
                var totalCount = await query.CountAsync();
                var pageCount = (int)Math.Ceiling((double)totalCount / pageSize);

                // 分页查询
                var structures = await query
                    .OrderBy(x => x.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(x => new
                    {
                        x.Id,
                        x.StructureName,
                        x.Description,
                        x.IsEnabled,
                        x.CreateTime,
                        x.CreateUser,
                        x.ModificationTime,
                        x.ModifierName
                    })
                    .ToListAsync();

                return Ok(new
                {
                    code = 200,
                    message = "查询成功",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = new
                    {
                        totalCount,
                        pageCount,
                        pageData = structures
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    code = 500,
                    message = $"查询失败: {ex.Message}",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = (object)null
                });
            }
        }

        /// <summary>
        /// 获取所有仓库结构（不分页）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetAllWarehouseStructures()
        {
            try
            {
                var structures = await _context.WarehouseStructures
                    .Where(x => x.IsEnabled)
                    .OrderBy(x => x.Id)
                    .Select(x => new
                    {
                        x.Id,
                        x.StructureName,
                        x.Description
                    })
                    .ToListAsync();

                return Ok(new
                {
                    code = 200,
                    message = "查询成功",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = structures
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    code = 500,
                    message = $"查询失败: {ex.Message}",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = (object)null
                });
            }
        }
    }
}
